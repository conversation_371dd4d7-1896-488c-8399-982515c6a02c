#!/usr/bin/env python3
"""
简单的档距差修复验证脚本
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_gap_difference_fix():
    """测试档距差修复"""
    print("档距差修复验证")
    print("=" * 50)
    
    try:
        # 检查修复的关键文件
        tab_file = "ui/tabs/historical_odds_interval_tab.py"
        app_file = "ui/modern_app.py"
        
        print("1. 检查关键文件修改...")
        
        # 检查历史区间分析标签页的修改
        with open(tab_file, 'r', encoding='utf-8') as f:
            tab_content = f.read()
        
        # 检查关键修改点
        checks = [
            ("team_db参数", "team_db=None" in tab_content),
            ("档距差计算方法", "_calculate_gap_difference" in tab_content),
            ("设置比赛数据方法", "set_match_data" in tab_content),
            ("移除硬编码", "gap_difference = 0.0" not in tab_content),
            ("调用计算方法", "gap_difference = self._calculate_gap_difference()" in tab_content)
        ]
        
        print("历史区间分析标签页修改检查:")
        for check_name, result in checks:
            status = "✓" if result else "✗"
            print(f"  {status} {check_name}")
        
        # 检查modern_app.py的修改
        with open(app_file, 'r', encoding='utf-8') as f:
            app_content = f.read()
        
        app_checks = [
            ("传递team_db参数", "team_db=self.team_db" in app_content),
            ("设置比赛数据", "set_match_data" in app_content)
        ]
        
        print("\nmodern_app.py修改检查:")
        for check_name, result in app_checks:
            status = "✓" if result else "✗"
            print(f"  {status} {check_name}")
        
        # 总结
        all_checks = [result for _, result in checks + app_checks]
        if all(all_checks):
            print("\n🎉 所有修改检查通过！")
            print("\n修复总结:")
            print("- 历史区间分析不再使用硬编码的档距差 0.0")
            print("- 现在会根据主客队实力正确计算档距差")
            print("- 区间分析结果将更加准确")
            return True
        else:
            print("\n❌ 部分修改检查失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def show_fix_details():
    """显示修复详情"""
    print("\n" + "=" * 50)
    print("修复详情")
    print("=" * 50)
    
    print("问题:")
    print("- 历史区间分析中档距差被硬编码为 0.0")
    print("- 导致区间值计算不准确: 区间值 = 档距 - 0.0 = 档距")
    
    print("\n解决方案:")
    print("1. 修改 HistoricalOddsIntervalTab 构造函数，增加 team_db 参数")
    print("2. 添加 _calculate_gap_difference() 方法计算实际档距差")
    print("3. 添加 set_match_data() 方法设置比赛数据")
    print("4. 修改 _perform_analysis() 使用计算的档距差")
    print("5. 修改 modern_app.py 传递 team_db 和比赛数据")
    
    print("\n修复后的计算流程:")
    print("1. 获取主客队名称")
    print("2. 通过 team_db.calculate_strength_gap() 计算档距差")
    print("3. 使用实际档距差进行区间分析")
    print("4. 区间值 = 档距 - 实际档距差")

if __name__ == "__main__":
    success = test_gap_difference_fix()
    show_fix_details()
    
    if success:
        print("\n✅ 档距差修复验证通过！")
    else:
        print("\n❌ 档距差修复验证失败！")
