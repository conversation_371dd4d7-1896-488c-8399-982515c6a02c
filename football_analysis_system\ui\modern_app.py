"""
现代化足球分析应用主窗口
使用现代化主题和组件重新设计的用户界面
"""

import os
import sys
import queue
import threading
import logging
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import sqlite3
import time

# 导入现代化UI组件
from .modern_theme import modern_theme, modern_style_manager, ModernCard
from .modern_components import (
    ModernTitleBar, ModernStatusBar, ModernLeagueSelector,
    ModernMatchList, ModernSearchBox, ModernButton
)

# 导入原有组件（需要适配）
from football_analysis_system.config import (
    APP_TITLE, APP_VERSION, TARGET_COMPANIES,
    DATA_URL_TEMPLATE, MATCH_HEADERS, ODDS_URL_TEMPLATE,
    ODDS_HEADERS_TEMPLATE, DB_MATCHES, DATA_DIR,
    DB_WILLIAM_HILL_TEAM, DB_STANDARD_ODDS, DB_STRENGTH_MATCHUP,
    DB_GUANGYISHILI
)

from football_analysis_system.models.match import Match
from football_analysis_system.models.team import Team
from football_analysis_system.models.odds import Odds

from football_analysis_system.db.match_db import MatchDatabase
from football_analysis_system.db.team_db import TeamDatabase

from football_analysis_system.analysis.odds_analyzer import OddsAnalyzer
from football_analysis_system.analysis.interval_analyzer import IntervalAnalyzer

from football_analysis_system.scrapers.match_scraper import MatchScraper
from football_analysis_system.scrapers.odds_scraper import OddsScraper

from football_analysis_system.utils.logger import setup_logger, TextWidgetHandler

# 导入标签页（需要适配主题）
from .tabs.basic_info_tab import BasicInfoTab
from .tabs.odds_analysis_tab import OddsAnalysisTab
from .tabs.interval_analysis_tab import IntervalAnalysisTab
from .tabs.historical_odds_interval_tab import HistoricalOddsIntervalTab
from .tabs.scraping_tab import ScrapingTab
from .tabs.poisson_tab import PoissonTab
from .tabs.advanced_poisson_tab import AdvancedPoissonTab
from .tabs.fundamental_analysis_tab import FundamentalAnalysisTab
from .tabs.data_management_tab import DataManagementTab
from .tabs.prediction_records_tab import PredictionRecordsTab
from .tabs.odds_rules_tab import OddsRulesTab
from .tabs.confidence_rules_tab import ConfidenceRulesTab


class ModernFootballAnalysisApp:
    """现代化足球比赛分析系统主应用类"""

    def __init__(self, root):
        """初始化现代化应用"""
        self.root = root
        self.theme = modern_theme
        self.style_manager = modern_style_manager

        # 应用主题样式
        self.style = self.style_manager.setup_styles(root)

        # 配置主窗口
        self._setup_window()

        # 数据存储
        self.matches = []
        self.leagues = []
        self.filtered_matches = []

        # 用户选择
        self.selected_league = tk.StringVar()
        self.selected_match_index = None
        self.selected_company_id = tk.StringVar(value="115")

        # 爬虫状态
        self.scraping_active = False
        self.message_queue = queue.Queue()

        # 初始化系统组件
        self._initialize_system()

        # 创建现代化界面
        self._create_modern_interface()

        # 加载数据
        self.load_all_data()

        # 设置事件处理
        self._setup_event_handlers()

    def _setup_window(self):
        """设置主窗口"""
        self.root.title(f"{APP_TITLE} - {APP_VERSION}")
        self.root.geometry("1600x1000")
        self.root.minsize(1200, 800)

        # 设置主题色
        self.root.configure(bg=self.theme.get_color("bg_primary"))

        # 设置窗口图标（如果有的话）
        try:
            # 这里可以设置应用图标
            pass
        except:
            pass

    def _initialize_system(self):
        """初始化系统组件"""
        # 初始化日志
        self._setup_logging()

        # 初始化数据库管理器
        self.match_db = MatchDatabase(DB_MATCHES)

        # 检查和创建广义实力数据库
        self._ensure_guangyishili_db()

        # 初始化球队数据库
        self.team_db = TeamDatabase(DB_GUANGYISHILI, self.selected_company_id.get())

        # 初始化分析器
        self.odds_analyzer = OddsAnalyzer(DB_STANDARD_ODDS)
        self.interval_analyzer = IntervalAnalyzer(DB_STRENGTH_MATCHUP)

        # 初始化爬虫
        self.match_scraper = MatchScraper(DATA_URL_TEMPLATE, MATCH_HEADERS)
        self.odds_scraper = OddsScraper(ODDS_URL_TEMPLATE, ODDS_HEADERS_TEMPLATE, TARGET_COMPANIES)

    def _setup_logging(self):
        """设置日志系统"""
        from football_analysis_system.utils.logger import setup_logger, TextWidgetHandler

        self.message_queue = queue.Queue()

        setup_logger(
            name="football_analysis",
            log_level=logging.INFO,
            log_file="football_analysis.log",
            message_queue=self.message_queue,
            text_widget=None
        )

        # 设置UI日志处理器
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        ui_handler = TextWidgetHandler(self.message_queue)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        ui_handler.setFormatter(formatter)
        root_logger.addHandler(ui_handler)
        root_logger.setLevel(logging.INFO)

        logging.info("现代化日志系统初始化完成")

    def _ensure_guangyishili_db(self):
        """确保广义实力数据库存在"""
        if os.path.exists(DB_GUANGYISHILI):
            logging.info(f"广义实力数据库文件存在: {DB_GUANGYISHILI}")
        else:
            logging.warning(f"广义实力数据库文件不存在: {DB_GUANGYISHILI}")
            if self._create_sample_guangyishili_db(DB_GUANGYISHILI):
                logging.info("示例广义实力数据库创建成功")
            else:
                logging.error("示例数据库创建失败")

    def _create_modern_interface(self):
        """创建现代化界面"""
        # 主容器
        self.main_container = tk.Frame(
            self.root,
            bg=self.theme.get_color("bg_primary")
        )
        self.main_container.pack(fill=tk.BOTH, expand=True)

        # 现代化标题栏
        self.title_bar = ModernTitleBar(
            self.main_container,
            title=APP_TITLE.replace("足球比赛分析系统", "足球数据分析中心"),
            on_scrape=self.on_scrape_button_click,
            on_cancel=self.on_cancel_scrape_click
        )
        self.title_bar.pack(fill=tk.X, padx=0, pady=0)

        # 主内容区域
        self._create_main_content()

        # 现代化状态栏
        self.status_bar = ModernStatusBar(
            self.main_container
        )
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)

    def _create_main_content(self):
        """创建主内容区域"""
        # 内容容器
        content_container = tk.Frame(
            self.main_container,
            bg=self.theme.get_color("bg_primary")
        )
        content_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 使用PanedWindow分割左右区域
        self.content_pane = tk.PanedWindow(
            content_container,
            orient=tk.HORIZONTAL,
            sashwidth=8,
            sashrelief=tk.FLAT,
            bg=self.theme.get_color("bg_primary"),
            sashpad=4
        )
        self.content_pane.pack(fill=tk.BOTH, expand=True)

        # 左侧面板（联赛选择和比赛列表）
        self._create_left_panel()

        # 右侧面板（分析标签页）
        self._create_right_panel()

    def _create_left_panel(self):
        """创建左侧面板"""
        # 左侧卡片容器
        left_card = ModernCard(
            self.content_pane,
            self.theme,
            title="数据浏览"
        )
        left_card.configure(width=450, bg=self.theme.get_color("bg_elevated"))
        self.content_pane.add(left_card, width=450, minsize=400)

        # 创建一个统一的搜索和筛选框架
        filter_frame = tk.Frame(
            left_card.content_frame,
            bg=self.theme.get_color("bg_elevated")
        )
        filter_frame.pack(fill=tk.X, pady=(0, 8))

        # 搜索和联赛选择的标题框架
        title_frame = tk.Frame(
            filter_frame,
            bg=self.theme.get_color("bg_elevated")
        )
        title_frame.pack(fill=tk.X, padx=5, pady=(5, 0))

        # 搜索标题
        search_title = tk.Label(
            title_frame,
            text="搜索与筛选",
            font=self.theme.get_font_config()["medium"],
            bg=self.theme.get_color("bg_elevated"),
            fg=self.theme.get_color("text_primary")
        )
        search_title.pack(side=tk.LEFT, pady=(0, 5))

        # 添加清除筛选按钮
        reset_button = ModernButton(
            title_frame,
            text="重置筛选",
            style="Secondary.TButton",
            command=self.reset_filters
        )
        reset_button.pack(side=tk.RIGHT, padx=5, pady=(0, 5))

        # 搜索和联赛选择的内容框架
        content_frame = tk.Frame(
            filter_frame,
            bg=self.theme.get_color("bg_elevated")
        )
        content_frame.pack(fill=tk.X, padx=5, pady=(0, 5))

        # 搜索框
        self.search_box = ModernSearchBox(
            content_frame,
            placeholder="搜索比赛或球队...",
            command=self._on_search
        )
        self.search_box.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 联赛下拉框
        self.selected_league = tk.StringVar()
        self.league_combobox = ttk.Combobox(
            content_frame,
            textvariable=self.selected_league,
            values=self.leagues,
            state="readonly",
            font=self.theme.get_font_config()["normal"],
            width=15
        )
        self.league_combobox.pack(side=tk.RIGHT, padx=(8, 0))
        self.league_combobox.bind("<<ComboboxSelected>>",
                                lambda e: self.on_league_selected(self.selected_league.get()))

        # 筛选状态标签
        self.filter_status = tk.Label(
            filter_frame,
            text="",
            font=self.theme.get_font_config()["small"],
            bg=self.theme.get_color("bg_elevated"),
            fg=self.theme.get_color("text_secondary")
        )
        self.filter_status.pack(anchor="w", padx=5, pady=(0, 5))

        # 比赛列表 - 给予更多空间
        match_list_frame = tk.Frame(
            left_card.content_frame,
            bg=self.theme.get_color("bg_elevated")
        )
        match_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        match_title = tk.Label(
            match_list_frame,
            text="比赛列表",
            font=self.theme.get_font_config()["medium"],
            bg=self.theme.get_color("bg_elevated"),
            fg=self.theme.get_color("text_primary")
        )
        match_title.pack(anchor="w", pady=(0, 8))

        self.match_list = ModernMatchList(
            match_list_frame,
            on_select=self._on_match_selected
        )
        self.match_list.pack(fill=tk.BOTH, expand=True)

    def _create_right_panel(self):
        """创建右侧分析面板"""
        # 右侧卡片容器
        right_card = ModernCard(
            self.content_pane,
            self.theme,
            title="分析工具"
        )
        right_card.configure(bg=self.theme.get_color("bg_elevated"))
        self.content_pane.add(right_card, minsize=750)

        # 设置标签页样式，确保标题完整显示
        tab_style = ttk.Style()
        tab_style.configure('TNotebook.Tab', padding=[10, 5], width=20)

        # 创建现代化标签页
        self.notebook = ttk.Notebook(right_card.content_frame, style='TNotebook')
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # 定义标签页索引
        self.TAB_BASIC = 0
        self.TAB_ODDS = 1
        self.TAB_FUNDAMENTAL = 2
        self.TAB_INTERVAL = 3
        self.TAB_HISTORICAL_INTERVAL = 4
        self.TAB_POISSON = 5
        self.TAB_DATA_MGMT = 6
        self.TAB_PREDICTION = 7
        self.TAB_SCRAPING = 8
        self.TAB_ODDS_RULES = 9
        self.TAB_CONFIDENCE_RULES = 10

        # 添加标签页（使用现有标签页但应用新主题）
        self._create_analysis_tabs()

        # 绑定标签切换事件
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)

    def _create_analysis_tabs(self):
        """创建分析标签页"""
        # 基本信息标签页
        self.basic_info_tab = BasicInfoTab(self.notebook)
        self.notebook.add(self.basic_info_tab, text="📊 基本信息")

        # 赔率分析标签页
        self.odds_analysis_tab = OddsAnalysisTab(self.notebook, self.odds_analyzer)
        self.notebook.add(self.odds_analysis_tab, text="💰 赔率分析")

        # 基本面分析标签页
        self.fundamental_analysis_tab = FundamentalAnalysisTab(self.notebook)
        self.notebook.add(self.fundamental_analysis_tab, text="🎯 基本面分析")

        # 区间分析标签页
        self.interval_analysis_tab = IntervalAnalysisTab(
            self.notebook,
            self.odds_analyzer,
            self.interval_analyzer,
            None,  # 不再传递fundamental_analysis_tab参数，使用None替代
            DB_MATCHES  # 直接传递数据库路径
        )
        self.notebook.add(self.interval_analysis_tab, text="📈 区间分析")

        # 历史赔率区间分析标签页
        self.historical_interval_tab = HistoricalOddsIntervalTab(
            self.notebook,
            self.odds_analyzer,
            self.interval_analyzer,
            DB_MATCHES,
            team_db=self.team_db
        )
        self.notebook.add(self.historical_interval_tab, text="📊 历史区间")

        # 高级泊松分析标签页
        self.advanced_poisson_tab = AdvancedPoissonTab(self.notebook)
        self.notebook.add(self.advanced_poisson_tab, text="🔬 泊松分析")

        # 数据管理标签页
        self.data_mgmt_tab = DataManagementTab(self.notebook, self.message_queue)
        self.notebook.add(self.data_mgmt_tab, text="🗂️ 数据管理")

        # 预测记录标签页
        self.prediction_records_tab = PredictionRecordsTab(self.notebook, DB_MATCHES)
        self.notebook.add(self.prediction_records_tab, text="📝 预测记录")

        # 数据更新标签页
        self.scraping_tab = ScrapingTab(self.notebook, self.message_queue)
        self.notebook.add(self.scraping_tab, text="🔄 数据更新")

        # 赔率法则标签页
        self.odds_rules_tab = OddsRulesTab(self.notebook)
        self.notebook.add(self.odds_rules_tab, text="📋 赔率法则")

        # 信心规则标签页
        self.confidence_rules_tab = ConfidenceRulesTab(self.notebook)
        self.notebook.add(self.confidence_rules_tab, text="🎮 信心规则")

    def _setup_event_handlers(self):
        """设置事件处理"""
        # 窗口事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.bind("<Configure>", self.on_window_resize)

        # 启动消息处理
        self.process_messages()

    def _on_search(self, search_text: str):
        """处理搜索事件"""
        if not search_text:
            # 重新显示所有比赛
            self.update_match_list()
            return

        # 搜索匹配的比赛
        search_text = search_text.lower()
        filtered_matches = []

        for match in self.filtered_matches:
            # 搜索主队、客队、联赛名称
            home_team = (match.home_team or "").lower()
            away_team = (match.away_team or "").lower()
            league = (match.league_name or "").lower()

            if (search_text in home_team or
                search_text in away_team or
                search_text in league):
                filtered_matches.append(match)

        # 更新比赛列表显示
        self._update_match_display(filtered_matches)

        # 更新状态
        self.status_bar.set_status(f"搜索到 {len(filtered_matches)} 场比赛")

    def _on_match_selected(self, index: int):
        """处理比赛选择事件"""
        if 0 <= index < len(self.filtered_matches):
            self.selected_match_index = index
            match_data = self.filtered_matches[index]
            logging.info(f"选中比赛: {match_data.home_team} vs {match_data.away_team} (联赛: {match_data.league_name})")
            self.display_match_details(match_data)
        else:
            logging.error(f"选中的比赛索引无效: {index}, 列表长度: {len(self.filtered_matches)}")

    def _update_match_display(self, matches):
        """更新比赛显示"""
        # 保存比赛顺序，确保与UI显示顺序一致
        self.filtered_matches = matches.copy()

        # 转换为显示格式
        display_matches = []
        for match in matches:
            # 检查赔率状态
            has_odds = bool(match.odds) if hasattr(match, 'odds') and match.odds else False
            has_hkjc = False

            if has_odds:
                for company_id, odds_data in match.odds.items():
                    if company_id == '432' or '香港马会' in str(company_id):
                        has_hkjc = True
                        break

            display_matches.append({
                'league': match.league_name or '未知联赛',
                'match_name': f"{match.home_team or '未知主队'} vs {match.away_team or '未知客队'}",
                'time': match.start_time or '未知时间',
                'has_odds': has_odds,
                'has_hkjc': has_hkjc
            })

        self.match_list.update_matches(display_matches)

    # 以下方法保持与原应用相同的逻辑，但适配新的UI组件

    def load_all_data(self):
        """加载所有数据"""
        try:
            self.status_bar.set_status("正在加载比赛数据...")

            # 加载球队数据
            self.team_db.load_team_data()

            # 设置默认公司ID
            companies = self.team_db.get_available_companies()
            if companies and len(companies) > 0:
                company_id, company_name = companies[0]
                self.selected_company_id.set(f"{company_name} ({company_id})")
            else:
                self.selected_company_id.set("115")  # 默认使用威廉希尔

            # 加载比赛
            self.load_matches()

            # 提取联赛
            self.extract_leagues()

            # 更新联赛选择器
            if hasattr(self, 'league_combobox'):
                self.league_combobox.configure(values=self.leagues)
                if self.leagues:
                    self.selected_league.set("全部联赛")

            # 更新比赛列表
            self.filtered_matches = self.matches
            self.update_match_list()

            self.status_bar.set_status(f"数据加载完成，共 {len(self.matches)} 场比赛")

        except Exception as e:
            self.status_bar.set_status(f"加载数据失败: {str(e)}")
            logging.error(f"加载数据失败: {str(e)}")

    def load_matches(self):
        """加载比赛数据"""
        self.matches = []
        self.filtered_matches = []

        # 从数据库加载比赛数据
        self.matches = self.match_db.load_matches_and_odds()
        self.filtered_matches = self.matches.copy()

        # 日志信息
        unique_leagues = set()
        for match in self.matches:
            if match.league_name:
                unique_leagues.add(match.league_name)

        logging.info(f"加载了 {len(self.matches)} 场比赛")
        logging.info(f"包含 {len(unique_leagues)} 个联赛")

    def extract_leagues(self):
        """从比赛数据中提取联赛列表"""
        league_set = set()

        # 添加所有联赛
        for match in self.matches:
            if match.league_name and match.league_name.strip():
                league_set.add(match.league_name.strip())

        # 转换为列表并排序
        league_list = sorted(list(league_set))

        # 添加"全部联赛"选项
        self.leagues = ["全部联赛"] + league_list

        logging.info(f"提取了 {len(league_list)} 个联赛")

    def update_match_list(self):
        """更新比赛列表"""
        if not self.filtered_matches:
            self.status_bar.set_status("没有比赛数据")
            return

        # 跨天时间排序
        def time_sort_key(match):
            try:
                time_str = match.start_time or '00:00'
                hour, minute = 0, 0
                if ':' in time_str:
                    parts = time_str.split(':')
                    hour = int(parts[0])
                    minute = int(parts[1]) if len(parts) > 1 else 0

                if hour >= 10:
                    return hour * 60 + minute
                else:
                    return (hour + 24) * 60 + minute
            except:
                return 99 * 60

        # 排序比赛
        sorted_matches = sorted(self.filtered_matches, key=time_sort_key)

        # 更新filtered_matches保持与UI顺序一致
        self.filtered_matches = sorted_matches

        # 更新显示
        self._update_match_display(sorted_matches)

        # 更新统计信息
        total_matches = len(sorted_matches)
        hkjc_count = 0
        odds_no_hkjc_count = 0
        no_odds_count = 0

        for match in sorted_matches:
            has_odds = bool(match.odds) if hasattr(match, 'odds') and match.odds else False
            if not has_odds:
                no_odds_count += 1
            else:
                has_hkjc = False
                for company_id, odds_data in match.odds.items():
                    if company_id == '432' or '香港马会' in str(company_id):
                        has_hkjc = True
                        break

                if has_hkjc:
                    hkjc_count += 1
                else:
                    odds_no_hkjc_count += 1

        status_message = f"显示 {total_matches} 场比赛 | 📊有马会:{hkjc_count} ⚠️无马会:{odds_no_hkjc_count} ❌无赔率:{no_odds_count} | 按时间排序"
        self.status_bar.set_status(status_message)

    def on_league_selected(self, league_name: str):
        """联赛选择事件处理"""
        logging.info(f"联赛选择: '{league_name}'")

        if not self.matches:
            self.filter_status.config(text="错误: 没有比赛数据")
            return

        if league_name == "全部联赛":
            self.filtered_matches = self.matches
            status_text = f"显示全部联赛: {len(self.filtered_matches)} 场比赛"
        else:
            self.filtered_matches = [match for match in self.matches
                                   if match.league_name and match.league_name.strip() == league_name.strip()]
            match_count = len(self.filtered_matches)

            if match_count == 0:
                status_text = f"未找到 '{league_name}' 联赛的比赛"
            else:
                status_text = f"筛选结果: {match_count} 场 '{league_name}' 联赛的比赛"

        self.update_match_list()
        self.filter_status.config(text=status_text)

    def display_match_details(self, match_data):
        """显示比赛详细信息"""
        if not match_data:
            logging.warning("尝试显示空的比赛数据")
            return

        try:
            logging.info(f"开始显示比赛详情: {match_data.home_team} vs {match_data.away_team} (联赛: {match_data.league_name})")
            self.status_bar.set_status(f"已选择: {match_data.home_team} vs {match_data.away_team}")

            # 获取主客队数据
            home_team_obj = self.team_db.get_team_data(match_data.home_team)
            away_team_obj = self.team_db.get_team_data(match_data.away_team)

            home_team_data = home_team_obj.to_dict()
            away_team_data = away_team_obj.to_dict()

            # 计算档距差
            gap_difference = self.team_db.calculate_strength_gap(
                match_data.home_team,
                match_data.away_team
            )
            logging.info(f"计算档距差: {gap_difference}")

            # 转换比赛数据
            match_dict = match_data.to_dict()
            match_dict['home_team_obj'] = home_team_obj
            match_dict['away_team_obj'] = away_team_obj

            # 更新基本信息标签页
            self.basic_info_tab.update_match_info(
                match_dict,
                home_team_data,
                away_team_data,
                gap_difference
            )

            # 按需更新其他标签页
            current_tab_index = self.notebook.index(self.notebook.select())
            logging.info(f"当前选中的标签页索引: {current_tab_index}")
            # 这里是真正切换比赛，不是标签页切换，所以使用is_tab_switch=False
            self._update_tab_content(current_tab_index, match_dict, gap_difference, is_tab_switch=False)

        except Exception as e:
            logging.error(f"显示比赛详情时出错: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("显示错误", f"显示比赛详情时出错: {e}")

    def _update_tab_content(self, tab_index: int, match_dict: dict, gap_difference: float, is_tab_switch: bool = False):
        """更新指定标签页内容"""
        home_team = match_dict.get('home_team', '未知')
        away_team = match_dict.get('away_team', '未知')
        league = match_dict.get('league_name', '未知')

        if tab_index == self.TAB_ODDS:
            logging.info(f"更新赔率分析标签页: {home_team} vs {away_team} (联赛: {league})")
            self.odds_analysis_tab.update_odds_data(match_dict)
        elif tab_index == self.TAB_INTERVAL:
            logging.info(f"更新区间分析标签页: {home_team} vs {away_team} (联赛: {league})")
            self.interval_analysis_tab.update_interval_data(match_dict, gap_difference)
        elif tab_index == self.TAB_HISTORICAL_INTERVAL:
            logging.info(f"更新历史赔率区间分析标签页: {home_team} vs {away_team} (联赛: {league})")
            match_id = match_dict.get('match_id', '')
            # 传递完整的比赛数据，而不仅仅是match_id
            if hasattr(self, 'filtered_matches') and self.selected_match_index is not None:
                match_data = self.filtered_matches[self.selected_match_index]
                self.historical_interval_tab.set_match_data(match_data)
            else:
                self.historical_interval_tab.set_match_id(match_id)
        elif tab_index == self.TAB_POISSON:
            logging.info(f"更新泊松分析标签页: {home_team} vs {away_team} (联赛: {league})")
            self.advanced_poisson_tab.update_match_info(match_dict, is_tab_switch)
        elif tab_index == self.TAB_FUNDAMENTAL:
            logging.info(f"更新基本面分析标签页: {home_team} vs {away_team} (联赛: {league})")
            self.fundamental_analysis_tab.update_match_info(match_dict, is_tab_switch)

    def on_company_selected(self, event=None):
        """公司选择事件处理"""
        selection = self.selected_company_id.get()
        company_id = selection.split("(")[-1].replace(")", "").strip()

        success = self.team_db.set_preferred_company(company_id)

        if success:
            if self.selected_match_index is not None:
                match_data = self.filtered_matches[self.selected_match_index]
                self.display_match_details(match_data)

            logging.info(f"已切换到公司: {self.team_db.current_company_name}")
        else:
            messagebox.showerror("错误", f"切换公司失败: {company_id}")

    # 爬虫相关方法
    def on_scrape_button_click(self):
        """爬取按钮点击事件处理"""
        if self.scraping_active:
            logging.warning("已有数据更新任务正在进行中")
            return

        self.notebook.select(self.TAB_SCRAPING)
        self.start_scraping()

    def on_cancel_scrape_click(self):
        """取消爬取按钮点击事件处理"""
        if not self.scraping_active:
            return

        logging.warning("正在取消数据更新...")
        self.scraping_active = False
        self.title_bar.set_scraping_state(False)
        self.scraping_tab.set_status("已取消")

    def start_scraping(self):
        """开始爬取数据"""
        self.scraping_tab.update_progress(0)
        self.scraping_tab.clear_log()
        self.title_bar.set_scraping_state(True)
        self.scraping_tab.set_status("正在运行")
        self.scraping_active = True

        scraper_thread = threading.Thread(target=self.scrape_data)
        scraper_thread.daemon = True
        scraper_thread.start()

        logging.info("开始更新数据...")

    def scrape_data(self):
        """爬虫主线程"""
        try:
            # 数据库设置
            logging.info("设置数据库并清除现有数据...")
            self.match_db.setup_database()
            self.match_db.clear_data()

            # 获取比赛数据
            logging.info("获取比赛数据...")
            self.update_progress(10)

            matches_df = self.match_scraper.run()
            if matches_df is None or matches_df.empty:
                logging.warning("过滤后未找到比赛，终止任务")
                self.finish_scraping(False)
                return

            match_count = len(matches_df)
            logging.info(f"找到 {match_count} 场需要处理的比赛")
            self.update_progress(40)

            # 获取赔率数据
            match_ids = matches_df['MatchID'].tolist()
            logging.info(f"开始获取 {len(match_ids)} 场比赛的赔率数据")
            all_odds_data = self.odds_scraper.get_odds_for_matches(match_ids)

            self.update_progress(80)

            # 保存数据
            if self.scraping_active:
                self.save_scraped_data(matches_df, all_odds_data)
                self.match_scraper.delete_temp_files()
                self.update_progress(95)

                # 重新加载数据
                logging.info("重新加载数据到界面...")
                self.load_matches()
                self.extract_leagues()
                self.league_combobox.configure(values=self.leagues)
                self.on_league_selected("全部联赛")

                self.status_bar.set_load_status(len(self.matches), len(self.leagues)-1)
                self.update_progress(100)
                logging.info("数据更新完成！")
                self.finish_scraping(True)
            else:
                logging.warning("数据更新已取消")
                self.finish_scraping(False)

        except Exception as e:
            logging.error(f"数据更新过程中出错: {e}")
            self.finish_scraping(False)

    def save_scraped_data(self, matches_df, all_odds_data):
        """保存爬取的数据"""
        try:
            logging.info("保存比赛和赔率数据...")
            self.match_db.save_matches_and_odds(matches_df.to_dict('records'), all_odds_data)

            # 统计信息
            company_counts = {}
            for match_id, bookmakers in all_odds_data.items():
                for bookmaker, odds in bookmakers.items():
                    if bookmaker not in company_counts:
                        company_counts[bookmaker] = 0
                    company_counts[bookmaker] += 1

            logging.info("各博彩公司赔率数据统计:")
            for company, count in company_counts.items():
                logging.info(f"  {company}: {count} 场比赛")

            # 保存到JSON
            self.match_db.save_to_json(
                matches_df,
                all_odds_data,
                os.path.join(DATA_DIR, 'matches_and_odds.json')
            )

            return True
        except Exception as e:
            logging.error(f"保存数据时出错: {e}")
            return False

    def finish_scraping(self, success):
        """完成数据抓取"""
        self.scraping_active = False
        self.update_ui_after_scraping(success)

    def update_ui_after_scraping(self, success):
        """抓取完成后更新UI"""
        self.title_bar.set_scraping_state(False)

        if success:
            # 重新加载数据
            self.load_all_data()
            self.status_bar.set_status("数据抓取成功，已更新")
        else:
            self.status_bar.set_status("数据抓取失败")

    def update_progress(self, value):
        """更新进度条"""
        logging.info(f"进度更新:{value}")

    def process_messages(self):
        """处理消息队列"""
        try:
            while not self.message_queue.empty():
                message = self.message_queue.get_nowait()

                if isinstance(message, tuple) and len(message) == 2 and message[0] == "update_progress":
                    value = message[1]
                    self.scraping_tab.update_progress(value)

                self.message_queue.task_done()
        except Exception as e:
            logging.error(f"处理消息队列时出错: {e}")
        finally:
            self.root.after(100, self.process_messages)

    def _create_sample_guangyishili_db(self, db_path):
        """创建示例广义实力数据库"""
        try:
            db_dir = os.path.dirname(db_path)
            if not os.path.exists(db_dir):
                os.makedirs(db_dir, exist_ok=True)

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            cursor.execute('''
            CREATE TABLE IF NOT EXISTS team_power_ratings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_id TEXT NOT NULL,
                company_name TEXT NOT NULL,
                team_name TEXT NOT NULL,
                power_category TEXT,
                power_level REAL,
                league_name TEXT,
                update_time TEXT DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # 示例数据
            sample_data = [
                ('281', '威廉希尔', '曼城', 'S', 1.0, '英超'),
                ('281', '威廉希尔', '利物浦', 'S', 0.9, '英超'),
                ('432', '香港马会', '曼城', 'S', 0.98, '英超'),
                ('432', '香港马会', '利物浦', 'S', 0.92, '英超'),
            ]

            cursor.executemany('''
            INSERT INTO team_power_ratings
                (company_id, company_name, team_name, power_category, power_level, league_name)
            VALUES (?, ?, ?, ?, ?, ?)
            ''', sample_data)

            conn.commit()
            conn.close()

            logging.info(f"已创建示例广义实力数据库: {db_path}")
            return True
        except Exception as e:
            logging.error(f"创建示例广义实力数据库失败: {e}")
            return False

    # 事件处理方法
    def on_tab_changed(self, event=None):
        """标签页切换事件处理"""
        try:
            current_tab_index = self.notebook.index(self.notebook.select())
            logging.info(f"切换到标签页: {current_tab_index}")

            if self.selected_match_index is not None:
                if 0 <= self.selected_match_index < len(self.filtered_matches):
                    match_data = self.filtered_matches[self.selected_match_index]
                    logging.info(f"标签页切换，显示比赛: {match_data.home_team} vs {match_data.away_team} (联赛: {match_data.league_name})")

                    match_dict = match_data.to_dict()
                    match_dict['home_team_obj'] = self.team_db.get_team_data(match_data.home_team)
                    match_dict['away_team_obj'] = self.team_db.get_team_data(match_data.away_team)

                    gap_difference = self.team_db.calculate_strength_gap(
                        match_data.home_team,
                        match_data.away_team
                    )

                    # 标记这是一个标签页切换事件，传递给_update_tab_content
                    self._update_tab_content(current_tab_index, match_dict, gap_difference)
                else:
                    logging.error(f"标签页切换，选中的比赛索引无效: {self.selected_match_index}, 列表长度: {len(self.filtered_matches)}")

        except Exception as e:
            logging.error(f"标签页切换事件处理出错: {e}")
            import traceback
            traceback.print_exc()

    def on_window_resize(self, event=None):
        """窗口大小变化事件处理"""
        if event and event.widget == self.root:
            try:
                window_width = self.root.winfo_width()
                logging.debug(f"窗口调整为宽度: {window_width}")
            except Exception as e:
                logging.error(f"调整窗口大小时出错: {e}")

    def on_closing(self):
        """关闭窗口事件处理"""
        if self.scraping_active:
            if messagebox.askyesno("确认", "数据更新正在进行中，确定要关闭程序吗？"):
                self.scraping_active = False
                self.root.destroy()
        else:
            self.root.destroy()

    def reset_filters(self):
        """重置所有筛选条件"""
        # 清空搜索框
        self.search_box.entry_var.set("")
        self.search_box.show_placeholder()

        # 重置联赛选择为"全部联赛"
        if len(self.leagues) > 0 and "全部联赛" in self.leagues:
            self.selected_league.set("全部联赛")
            self.on_league_selected("全部联赛")

        # 显示所有比赛
        self.filtered_matches = self.matches
        self.update_match_list()

        # 更新状态提示
        self.filter_status.config(text=f"已重置筛选，显示全部 {len(self.matches)} 场比赛")

        # 记录日志
        logging.info(f"已重置筛选条件，显示全部 {len(self.matches)} 场比赛")


# 启动现代化应用的函数
def run_modern_app():
    """启动现代化足球分析应用"""
    root = tk.Tk()
    app = ModernFootballAnalysisApp(root)
    root.mainloop()


if __name__ == "__main__":
    run_modern_app()