#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试芬超联赛爬取功能
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
football_system_path = os.path.join(project_root, 'football_analysis_system')
sys.path.insert(0, project_root)
sys.path.insert(0, football_system_path)

def test_finland_crawl():
    """测试芬超联赛爬取"""
    print("=== 测试芬超联赛爬取功能 ===")
    
    try:
        # 导入必要的模块
        from football_analysis_system.scrapers.config import LEAGUES
        from football_analysis_system.scrapers.match_odds_History import FootballCrawler
        
        print("\n1. 检查芬超配置:")
        if "FIN1" in LEAGUES:
            fin_config = LEAGUES["FIN1"]
            print(f"✓ 芬超配置存在")
            print(f"  - 名称: {fin_config['name']}")
            print(f"  - URL: {fin_config['url']}")
        else:
            print("✗ 芬超配置不存在")
            return
            
        print("\n2. 初始化爬虫:")
        crawler = FootballCrawler()
        
        # 检查目标联赛中是否包含芬超
        if "FIN1" in crawler.target_leagues:
            print("✓ 芬超在目标联赛列表中")
            print(f"  - 目标联赛总数: {len(crawler.target_leagues)}")
        else:
            print("✗ 芬超不在目标联赛列表中")
            return
            
        print("\n3. 测试芬超URL访问:")
        try:
            import requests
            url = fin_config['url']
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"✓ 芬超URL可访问 (状态码: {response.status_code})")
                print(f"  - 响应长度: {len(response.text)} 字符")
                
                # 检查响应内容是否包含预期的数据结构
                content = response.text
                if 'arrLeague' in content and 'arrTeam' in content:
                    print("✓ 响应包含预期的数据结构")
                else:
                    print("⚠ 响应可能不包含完整的数据结构")
                    
            else:
                print(f"✗ 芬超URL访问失败 (状态码: {response.status_code})")
        except Exception as e:
            print(f"✗ 芬超URL访问异常: {e}")
            
        print("\n4. 测试单独爬取芬超:")
        try:
            # 设置只爬取芬超
            crawler.target_leagues = {"FIN1": LEAGUES["FIN1"]}
            
            print("开始爬取芬超数据...")
            success = crawler.crawl_league("FIN1", LEAGUES["FIN1"])
            
            if success:
                print("✓ 芬超数据爬取成功")
            else:
                print("✗ 芬超数据爬取失败")
                
        except Exception as e:
            print(f"✗ 芬超爬取异常: {e}")
            import traceback
            traceback.print_exc()
            
    except ImportError as e:
        print(f"✗ 导入模块失败: {e}")
    except Exception as e:
        print(f"✗ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n=== 测试完成 ===")

def test_finland_in_database():
    """检查数据库中是否有芬超数据"""
    print("\n=== 检查数据库中的芬超数据 ===")
    
    try:
        import sqlite3
        from football_analysis_system.scrapers.config import DB_FILE
        
        print(f"数据库路径: {DB_FILE}")
        
        if not os.path.exists(DB_FILE):
            print("✗ 数据库文件不存在")
            return
            
        with sqlite3.connect(DB_FILE) as conn:
            cursor = conn.cursor()
            
            # 查询芬超相关数据
            cursor.execute("SELECT DISTINCT league_name FROM matches WHERE league_name LIKE '%芬%' OR league_name LIKE '%Finland%' OR league_name LIKE '%Veikkausliiga%'")
            finland_leagues = cursor.fetchall()
            
            if finland_leagues:
                print("✓ 数据库中找到芬兰相关联赛:")
                for league in finland_leagues:
                    print(f"  - {league[0]}")
                    
                    # 统计该联赛的比赛数量
                    cursor.execute("SELECT COUNT(*) FROM matches WHERE league_name = ?", (league[0],))
                    match_count = cursor.fetchone()[0]
                    print(f"    比赛数量: {match_count}")
            else:
                print("✗ 数据库中未找到芬兰相关联赛数据")
                
            # 查询所有联赛
            cursor.execute("SELECT DISTINCT league_name FROM matches ORDER BY league_name")
            all_leagues = cursor.fetchall()
            print(f"\n数据库中共有 {len(all_leagues)} 个联赛:")
            for league in all_leagues[:10]:  # 只显示前10个
                print(f"  - {league[0]}")
            if len(all_leagues) > 10:
                print(f"  ... 还有 {len(all_leagues) - 10} 个联赛")
                
    except Exception as e:
        print(f"✗ 检查数据库时发生异常: {e}")

if __name__ == "__main__":
    test_finland_crawl()
    test_finland_in_database()
