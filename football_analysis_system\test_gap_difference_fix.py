#!/usr/bin/env python3
"""
测试档距差修复的脚本

这个脚本验证历史区间分析中档距差计算的修复是否正确工作。
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from analysis.odds_analyzer import OddsAnalyzer
from analysis.interval_analyzer import IntervalAnalyzer
from db.team_db import TeamDatabase
from ui.tabs.historical_odds_interval_tab import HistoricalOddsIntervalTab
from config import DB_STANDARD_ODDS, DB_STRENGTH_MATCHUP, DB_GUANGYISHILI, DB_MATCHES

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class MockMatchData:
    """模拟比赛数据"""
    def __init__(self, home_team, away_team, match_id="test_match_001"):
        self.home_team = home_team
        self.away_team = away_team
        self.match_id = match_id

def test_gap_difference_calculation():
    """测试档距差计算"""
    print("=" * 60)
    print("测试档距差计算修复")
    print("=" * 60)
    
    try:
        # 初始化分析器
        print("1. 初始化分析器...")
        odds_analyzer = OddsAnalyzer(DB_STANDARD_ODDS)
        interval_analyzer = IntervalAnalyzer(DB_STRENGTH_MATCHUP)
        team_db = TeamDatabase(DB_GUANGYISHILI, "1")  # 使用默认公司ID
        
        print("✓ 分析器初始化成功")
        
        # 创建模拟比赛数据
        print("\n2. 创建模拟比赛数据...")
        mock_match = MockMatchData("曼城", "利物浦")
        print(f"✓ 模拟比赛: {mock_match.home_team} vs {mock_match.away_team}")
        
        # 测试档距差计算
        print("\n3. 测试档距差计算...")
        gap_difference = team_db.calculate_strength_gap(mock_match.home_team, mock_match.away_team)
        
        if gap_difference is not None:
            print(f"✓ 档距差计算成功: {gap_difference}")
        else:
            print("⚠ 档距差计算返回None，可能是球队数据不存在")
        
        # 创建历史区间分析标签页实例（不创建UI）
        print("\n4. 测试历史区间分析标签页...")
        
        # 创建一个简单的根窗口用于测试
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 创建标签页实例
        tab = HistoricalOddsIntervalTab(
            parent=root,
            odds_analyzer=odds_analyzer,
            interval_analyzer=interval_analyzer,
            db_path=DB_MATCHES,
            team_db=team_db
        )
        
        print("✓ 历史区间分析标签页创建成功")
        
        # 设置比赛数据并测试档距差计算
        print("\n5. 测试标签页中的档距差计算...")
        tab.set_match_data(mock_match)
        calculated_gap = tab._calculate_gap_difference()
        
        print(f"✓ 标签页计算的档距差: {calculated_gap}")
        
        # 验证结果一致性
        if gap_difference == calculated_gap:
            print("✓ 档距差计算结果一致")
        else:
            print(f"⚠ 档距差计算结果不一致: {gap_difference} vs {calculated_gap}")
        
        # 清理
        root.destroy()
        
        print("\n" + "=" * 60)
        print("测试完成！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_before_after_comparison():
    """对比修复前后的行为"""
    print("\n" + "=" * 60)
    print("修复前后对比")
    print("=" * 60)
    
    print("修复前:")
    print("- 档距差硬编码为 0.0")
    print("- 区间值 = 档距 - 0.0 = 档距")
    print("- 这导致区间分析结果不准确")
    
    print("\n修复后:")
    print("- 档距差通过 team_db.calculate_strength_gap() 正确计算")
    print("- 区间值 = 档距 - 实际档距差")
    print("- 区间分析结果更加准确")
    
    print("\n关键改动:")
    print("1. HistoricalOddsIntervalTab 构造函数增加 team_db 参数")
    print("2. 添加 _calculate_gap_difference() 方法")
    print("3. 添加 set_match_data() 方法")
    print("4. _perform_analysis() 中使用计算的档距差而非硬编码值")
    print("5. modern_app.py 中传递 team_db 参数并设置比赛数据")

if __name__ == "__main__":
    print("档距差修复测试脚本")
    print("=" * 60)
    
    # 运行测试
    success = test_gap_difference_calculation()
    
    # 显示对比信息
    test_before_after_comparison()
    
    if success:
        print("\n🎉 所有测试通过！档距差修复成功。")
    else:
        print("\n❌ 测试失败，请检查修复。")
