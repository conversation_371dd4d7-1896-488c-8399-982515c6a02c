# 区间分析一致性修复说明

## 问题描述

用户发现在相同时间点的赔率下，**区间分析标签页**和**历史区间分析标签页**显示的开盘区间不一致。

## 根本原因

两个标签页在计算档距差时使用了不同的逻辑：

### 区间分析标签页
- 使用 `company_gap_diffs` 字典
- 每个博彩公司可能有不同的档距差（基于多公司评分数据）
- 计算流程：
  1. 获取主客队的多公司评分数据
  2. 为每个公司计算专属的档距差
  3. 根据博彩公司名称匹配对应的档距差
  4. 使用公司特定的档距差计算区间

### 历史区间分析标签页（修复前）
- 使用统一的档距差（通过 `_calculate_gap_difference()` 计算）
- 所有时间点和所有公司都使用相同的档距差
- 计算流程：
  1. 计算一个默认的档距差
  2. 所有历史数据点都使用这个档距差

## 修复方案

### 1. 新增方法 `_get_company_gap_difference()`

```python
def _get_company_gap_difference(self, company_id):
    """获取指定公司的档距差"""
    # 1. 获取主客队对象
    # 2. 查找指定公司的多公司评分数据
    # 3. 计算公司特定的档距差
    # 4. 如果没有找到，回退到默认方法
```

### 2. 修改分析流程

修改 `_perform_analysis()` 方法：

```python
# 修复前
gap_difference = self._calculate_gap_difference()

# 修复后  
gap_difference = self._get_company_gap_difference(self.current_company_id)
```

### 3. 保持向后兼容

- 保留原有的 `_calculate_gap_difference()` 方法作为回退选项
- 当没有公司特定数据时，自动使用默认方法

## 修复效果

### 修复前
- 区间分析：使用公司A的档距差 → 显示"低水区间"
- 历史区间分析：使用默认档距差 → 显示"中水区间"
- **结果不一致** ❌

### 修复后
- 区间分析：使用公司A的档距差 → 显示"低水区间"  
- 历史区间分析：使用公司A的档距差 → 显示"低水区间"
- **结果一致** ✅

## 技术细节

### 档距差计算公式
```
档距差 = 主队评分 - 客队评分
```

### 区间值计算公式
```
区间值 = 档距 - 档距差
```

### 多公司评分数据结构
```python
multi_company_ratings = {
    "432": {  # 香港马会
        "rating": 85.5,
        "company_name": "香港马会"
    },
    "80": {   # 澳门
        "rating": 84.2, 
        "company_name": "澳门"
    }
    # ... 其他公司
}
```

## 验证方法

1. 选择同一场比赛
2. 在区间分析标签页查看某公司的开盘区间
3. 在历史区间分析标签页选择相同公司
4. 对比相同时间点的区间分类
5. 确认结果一致

## 影响范围

- ✅ 提高了分析结果的准确性
- ✅ 确保了不同标签页间的一致性  
- ✅ 保持了向后兼容性
- ✅ 不影响现有功能的正常使用

## 注意事项

1. 如果球队没有多公司评分数据，会自动回退到默认档距差
2. 修复只影响历史区间分析的计算逻辑，不改变区间分析标签页
3. 建议在使用前确保球队数据库包含完整的多公司评分信息
