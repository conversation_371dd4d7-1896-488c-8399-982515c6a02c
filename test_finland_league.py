#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试芬超联赛配置
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
football_system_path = os.path.join(project_root, 'football_analysis_system')
sys.path.insert(0, project_root)
sys.path.insert(0, football_system_path)

def test_finland_league_config():
    """测试芬超联赛配置"""
    print("=== 测试芬超联赛配置 ===")
    
    try:
        # 测试联赛URL配置
        from scrapers.config import LEAGUES
        
        print("\n1. 测试联赛URL配置:")
        if "FIN1" in LEAGUES:
            fin_config = LEAGUES["FIN1"]
            print(f"✓ 芬超配置存在")
            print(f"  - 名称: {fin_config['name']}")
            print(f"  - 英文名: {fin_config.get('english_name', 'N/A')}")
            print(f"  - 简称: {fin_config.get('short_name', 'N/A')}")
            print(f"  - URL: {fin_config['url']}")
            print(f"  - 描述: {fin_config.get('description', 'N/A')}")
        else:
            print("✗ 芬超配置不存在")
            
    except Exception as e:
        print(f"✗ 联赛URL配置测试失败: {e}")
    
    try:
        # 测试联赛映射器
        from core.league_mapper import LeagueMapper
        
        print("\n2. 测试联赛映射器:")
        
        # 测试标准化名称
        test_names = ["芬超", "Veikkausliiga", "Finnish Veikkausliiga", "芬兰超级联赛"]
        for name in test_names:
            normalized = LeagueMapper.normalize_league_name(name)
            print(f"  {name} -> {normalized}")
            
        # 测试联赛代码
        code = LeagueMapper.get_league_code("芬超")
        print(f"  芬超联赛代码: {code}")
        
        # 测试是否支持
        supported = LeagueMapper.is_supported_league("芬超")
        print(f"  是否支持芬超: {supported}")
        
    except Exception as e:
        print(f"✗ 联赛映射器测试失败: {e}")
    
    try:
        # 测试联赛映射
        from scrapers.league_mapping import LEAGUE_STANDARD_NAMES, LEAGUE_ALIASES, LEAGUE_REGIONS
        
        print("\n3. 测试联赛映射:")
        
        # 测试标准名称映射
        if "芬超" in LEAGUE_STANDARD_NAMES:
            aliases = LEAGUE_STANDARD_NAMES["芬超"]
            print(f"  ✓ 芬超标准名称映射存在: {aliases}")
        else:
            print("  ✗ 芬超标准名称映射不存在")
            
        # 测试别名
        if "芬超" in LEAGUE_ALIASES:
            aliases = LEAGUE_ALIASES["芬超"]
            print(f"  ✓ 芬超别名存在: {aliases}")
        else:
            print("  ✗ 芬超别名不存在")
            
        # 测试地区分类
        found_in_region = False
        for region, leagues in LEAGUE_REGIONS.items():
            if "芬超" in leagues:
                print(f"  ✓ 芬超在地区分类中: {region}")
                found_in_region = True
                break
        if not found_in_region:
            print("  ✗ 芬超不在任何地区分类中")
            
    except Exception as e:
        print(f"✗ 联赛映射测试失败: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_finland_league_config()
